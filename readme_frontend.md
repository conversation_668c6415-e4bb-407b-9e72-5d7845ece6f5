# 🎙️ Frontend Integration Guide for Gemini Live WebRTC Server

This guide provides comprehensive instructions for integrating your frontend application with the Gemini Live WebRTC server for real-time voice conversations with AI.

## 📋 Table of Contents

- [Overview](#overview)
- [Server Setup](#server-setup)
- [Frontend Integration](#frontend-integration)
- [API Endpoints](#api-endpoints)
- [WebRTC Connection](#webrtc-connection)
- [Complete Examples](#complete-examples)
- [Troubleshooting](#troubleshooting)
- [Advanced Configuration](#advanced-configuration)

## 🔍 Overview

The Gemini Live WebRTC server provides real-time audio streaming capabilities that integrate with Google's Gemini API. The server handles:

- **Real-time audio processing** via WebRTC
- **AI voice responses** using Gemini's live voice capabilities
- **Tool integration** for enhanced AI functionality (e.g., product search)
- **Automatic speech detection** with configurable sensitivity
- **CORS support** for web applications

### Architecture Flow

```
Frontend (WebRTC) ↔ FastAPI Server ↔ Gemini API
```

## 🚀 Server Setup

### 1. Environment Configuration

Create a `.env` file in your server directory:

```env
GOOGLE_API_KEY=your_gemini_api_key_here
```

### 2. Server Configuration

The server is configured in `test.py` with the following key parameters:

```python
from gemini_live_voice_only_pkg.stream_server import create_gemini_stream
from gemini_live_voice_only_pkg.prompts import system_prompt
import uvicorn
import os
from dotenv import load_dotenv

load_dotenv()

# Configuration
API_KEY = os.environ.get("GOOGLE_API_KEY")
VOICE_NAME = "Puck"  # Available: Puck, Charon, Kore, Fenrir, Aoede

app = create_gemini_stream(
    api_key=API_KEY,
    system_prompt=system_prompt,
    voice_name=VOICE_NAME,
    cors_origins=["http://localhost:3000", "https://yourdomain.com"],
    cors_allow_credentials=True,
    cors_allow_methods=["*"],
    cors_allow_headers=["*"],
)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8680, reload=False)
```

### 3. Start the Server

```bash
# Using Python directly
python test.py

# Using Docker
docker-compose up

# Using UV (recommended)
uv run python test.py
```

The server will be available at `http://localhost:8680`

## 🌐 Frontend Integration

### Required Dependencies

For a web frontend, you'll need:

```bash
# For React/Next.js projects
npm install uuid

# No additional WebRTC libraries needed - uses browser native APIs
```

### Basic HTML Setup

```html
<!DOCTYPE html>
<html>
<head>
    <title>Gemini Live Voice Chat</title>
</head>
<body>
    <div id="app">
        <button id="startCall">Start Voice Chat</button>
        <button id="endCall" disabled>End Chat</button>
        <div id="status">Ready to connect</div>
        <audio id="remoteAudio" autoplay></audio>
    </div>
    <script src="webrtc-client.js"></script>
</body>
</html>
```

## 📡 API Endpoints

### Base URL
```
http://localhost:8680
```

### Available Endpoints

#### 1. WebRTC Offer Endpoint
```http
POST /offer
Content-Type: application/json

{
    "sdp": "v=0\r\no=...",
    "type": "offer"
}
```

**Response:**
```json
{
    "sdp": "v=0\r\no=...",
    "type": "answer"
}
```

#### 2. Input Hook Endpoint
```http
POST /input_hook
Content-Type: application/json

{
    "webrtc_id": "unique-session-id"
}
```

**Response:**
```json
{
    "status": "ok"
}
```

#### 3. Health Check
```http
GET /docs
```
Returns FastAPI documentation interface.

## 🔗 WebRTC Connection

### Complete JavaScript Implementation

```javascript
class GeminiVoiceClient {
    constructor(serverUrl = 'http://localhost:8680') {
        this.serverUrl = serverUrl;
        this.peerConnection = null;
        this.localStream = null;
        this.sessionId = null;
        this.isConnected = false;
    }

    // Generate unique session ID
    generateSessionId() {
        return 'session-' + Math.random().toString(36).substr(2, 9) + '-' + Date.now();
    }

    // Initialize WebRTC connection
    async startVoiceChat() {
        try {
            this.sessionId = this.generateSessionId();
            
            // Get user media (microphone)
            this.localStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true,
                    sampleRate: 16000
                },
                video: false
            });

            // Create peer connection
            this.peerConnection = new RTCPeerConnection({
                iceServers: [
                    { urls: 'stun:stun.l.google.com:19302' },
                    { urls: 'stun:stun1.l.google.com:19302' }
                ]
            });

            // Add local stream to peer connection
            this.localStream.getTracks().forEach(track => {
                this.peerConnection.addTrack(track, this.localStream);
            });

            // Handle remote stream
            this.peerConnection.ontrack = (event) => {
                const remoteAudio = document.getElementById('remoteAudio');
                if (remoteAudio) {
                    remoteAudio.srcObject = event.streams[0];
                }
            };

            // Handle ICE candidates
            this.peerConnection.onicecandidate = (event) => {
                if (event.candidate) {
                    console.log('ICE candidate:', event.candidate);
                }
            };

            // Create and send offer
            const offer = await this.peerConnection.createOffer();
            await this.peerConnection.setLocalDescription(offer);

            // Send offer to server
            const response = await fetch(`${this.serverUrl}/offer`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    sdp: offer.sdp,
                    type: offer.type
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const answer = await response.json();
            
            // Set remote description
            await this.peerConnection.setRemoteDescription(new RTCSessionDescription(answer));

            // Send session ID to input hook
            await this.sendInputHook();

            this.isConnected = true;
            this.updateStatus('Connected - You can now speak!');
            
        } catch (error) {
            console.error('Error starting voice chat:', error);
            this.updateStatus(`Error: ${error.message}`);
        }
    }

    // Send session ID to server
    async sendInputHook() {
        try {
            const response = await fetch(`${this.serverUrl}/input_hook`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    webrtc_id: this.sessionId
                })
            });

            if (!response.ok) {
                throw new Error(`Input hook failed: ${response.status}`);
            }

            console.log('Input hook successful');
        } catch (error) {
            console.error('Error sending input hook:', error);
            throw error;
        }
    }

    // End voice chat
    async endVoiceChat() {
        try {
            if (this.localStream) {
                this.localStream.getTracks().forEach(track => track.stop());
                this.localStream = null;
            }

            if (this.peerConnection) {
                this.peerConnection.close();
                this.peerConnection = null;
            }

            this.isConnected = false;
            this.sessionId = null;
            this.updateStatus('Disconnected');
            
        } catch (error) {
            console.error('Error ending voice chat:', error);
        }
    }

    // Update status display
    updateStatus(message) {
        const statusElement = document.getElementById('status');
        if (statusElement) {
            statusElement.textContent = message;
        }
        console.log('Status:', message);
    }
}

// Initialize client
const voiceClient = new GeminiVoiceClient();

// Event listeners
document.addEventListener('DOMContentLoaded', () => {
    const startButton = document.getElementById('startCall');
    const endButton = document.getElementById('endCall');

    startButton.addEventListener('click', async () => {
        startButton.disabled = true;
        endButton.disabled = false;
        await voiceClient.startVoiceChat();
    });

    endButton.addEventListener('click', async () => {
        startButton.disabled = false;
        endButton.disabled = true;
        await voiceClient.endVoiceChat();
    });
});
```

## 🔧 Complete Examples

### React Component Example

```jsx
import React, { useState, useRef, useCallback } from 'react';

const GeminiVoiceChat = () => {
    const [isConnected, setIsConnected] = useState(false);
    const [status, setStatus] = useState('Ready to connect');
    const peerConnectionRef = useRef(null);
    const localStreamRef = useRef(null);
    const sessionIdRef = useRef(null);

    const generateSessionId = () => {
        return 'session-' + Math.random().toString(36).substr(2, 9) + '-' + Date.now();
    };

    const startVoiceChat = useCallback(async () => {
        try {
            setStatus('Connecting...');
            sessionIdRef.current = generateSessionId();
            
            // Get user media
            localStreamRef.current = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true,
                    sampleRate: 16000
                },
                video: false
            });

            // Create peer connection
            peerConnectionRef.current = new RTCPeerConnection({
                iceServers: [
                    { urls: 'stun:stun.l.google.com:19302' },
                    { urls: 'stun:stun1.l.google.com:19302' }
                ]
            });

            // Add tracks
            localStreamRef.current.getTracks().forEach(track => {
                peerConnectionRef.current.addTrack(track, localStreamRef.current);
            });

            // Handle remote stream
            peerConnectionRef.current.ontrack = (event) => {
                const audio = new Audio();
                audio.srcObject = event.streams[0];
                audio.play();
            };

            // Create offer
            const offer = await peerConnectionRef.current.createOffer();
            await peerConnectionRef.current.setLocalDescription(offer);

            // Send to server
            const response = await fetch('http://localhost:8680/offer', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(offer)
            });

            const answer = await response.json();
            await peerConnectionRef.current.setRemoteDescription(answer);

            // Send input hook
            await fetch('http://localhost:8680/input_hook', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ webrtc_id: sessionIdRef.current })
            });

            setIsConnected(true);
            setStatus('Connected - Speak now!');
            
        } catch (error) {
            setStatus(`Error: ${error.message}`);
        }
    }, []);

    const endVoiceChat = useCallback(async () => {
        if (localStreamRef.current) {
            localStreamRef.current.getTracks().forEach(track => track.stop());
        }
        if (peerConnectionRef.current) {
            peerConnectionRef.current.close();
        }
        setIsConnected(false);
        setStatus('Disconnected');
    }, []);

    return (
        <div>
            <h2>Gemini Voice Chat</h2>
            <p>Status: {status}</p>
            <button onClick={startVoiceChat} disabled={isConnected}>
                Start Chat
            </button>
            <button onClick={endVoiceChat} disabled={!isConnected}>
                End Chat
            </button>
        </div>
    );
};

export default GeminiVoiceChat;
```

## 🐛 Troubleshooting

### Common Issues

#### 1. CORS Errors
**Problem:** Browser blocks requests due to CORS policy.
**Solution:** Update server CORS configuration:
```python
cors_origins=["http://localhost:3000", "https://yourdomain.com"]
```

#### 2. Microphone Permission Denied
**Problem:** Browser doesn't have microphone access.
**Solution:** Ensure HTTPS or localhost, check browser permissions.

#### 3. WebRTC Connection Fails
**Problem:** Peer connection doesn't establish.
**Solution:** 
- Check ICE servers configuration
- Verify network connectivity
- Test with different browsers

#### 4. No Audio Output
**Problem:** Can't hear AI responses.
**Solution:**
- Check audio element autoplay policy
- Verify remote stream handling
- Test browser audio permissions

### Debug Tips

```javascript
// Enable WebRTC debugging
peerConnection.oniceconnectionstatechange = () => {
    console.log('ICE connection state:', peerConnection.iceConnectionState);
};

peerConnection.onconnectionstatechange = () => {
    console.log('Connection state:', peerConnection.connectionState);
};
```

## ⚙️ Advanced Configuration

### Server Configuration Options

```python
app = create_gemini_stream(
    api_key=API_KEY,
    system_prompt=system_prompt,
    voice_name=VOICE_NAME,
    expected_layout="mono",           # Audio layout
    output_sample_rate=24000,         # AI voice sample rate
    output_frame_size=480,            # Frame size for processing
    input_sample_rate=16000,          # Input microphone sample rate
    ice_servers=[                     # Custom ICE servers
        {"urls": "stun:stun.l.google.com:19302"},
        {"urls": "turn:your-turn-server.com", "username": "user", "credential": "pass"}
    ],
    concurrency_limit=50,             # Max concurrent connections
    time_limit=900,                   # Session timeout (seconds)
    cors_origins=["*"],               # Allowed origins
)
```

### Voice Options

Available Gemini voices:
- `Puck` - Friendly, conversational
- `Charon` - Deep, authoritative  
- `Kore` - Warm, professional
- `Fenrir` - Energetic, dynamic
- `Aoede` - Calm, soothing

### Custom System Prompts

Modify the AI behavior by updating the system prompt:

```python
custom_prompt = """
You are a helpful customer service agent for an e-commerce platform.
Your goal is to assist customers with product inquiries and orders.
Always be polite, professional, and helpful.
Use the search_products tool when customers ask about specific items.
"""

app = create_gemini_stream(
    api_key=API_KEY,
    system_prompt=custom_prompt,
    voice_name=VOICE_NAME,
)
```

## 📚 Additional Resources

- [FastRTC Documentation](https://github.com/livekit/fastrtc)
- [Google Gemini API Docs](https://ai.google.dev/docs)
- [WebRTC MDN Guide](https://developer.mozilla.org/en-US/docs/Web/API/WebRTC_API)
- [FastAPI CORS Guide](https://fastapi.tiangolo.com/tutorial/cors/)

## 🤝 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review server logs for error messages
3. Test with the provided examples
4. Verify all dependencies are installed correctly

---

**Happy coding! 🚀**
